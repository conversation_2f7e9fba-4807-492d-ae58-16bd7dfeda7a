import { Button } from "@/components/ui/button";
import { cn } from "@/utils/cn";
import { type Token } from "@/services";

interface SwapActionButtonProps {
  onClick: () => void;
  disabled: boolean;
  isSwapping: boolean;
  isValid: boolean;
  sellToken: Token | null;
  buyToken: Token | null;
  swapSuccess: boolean;
  buttonContent: React.ReactNode;
}

export function SwapActionButton({
  onClick,
  disabled,
  isSwapping,
  isValid,
  sellToken,
  buyToken,
  swapSuccess,
  buttonContent,
}: SwapActionButtonProps) {
  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "w-full mt-6 font-medium py-3 rounded-xl transition-all duration-200",
        swapSuccess
          ? "bg-green-500 text-white cursor-not-allowed"
          : isSwapping || !isValid || !sellToken || !buyToken
          ? "bg-white/70 text-black/70 cursor-not-allowed"
          : "bg-white hover:bg-gray-100 text-black hover:scale-[1.02]"
      )}
    >
      {buttonContent}
    </Button>
  );
}
