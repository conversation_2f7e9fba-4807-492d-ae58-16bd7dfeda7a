@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --radius: 0.5rem;
  }
}

/* Global macOS-style scrollbar for all elements */
* {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background: transparent;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 9999px;
  transition: background-color 0.2s ease;
}

*:hover::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

*::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.7);
}

@layer utilities {
  /* Enhanced scrollbar for specific elements that need more visibility */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.3);
    border-radius: 9999px;
    transition: background-color 0.2s ease;
  }

  .scrollbar-thin:hover::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.6);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(107, 114, 128, 0.8);
  }
}
