import { ArrowUpDown } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

interface SwapButtonProps {
  onClick: () => void;
}

export function SwapButton({ onClick }: SwapButtonProps) {
  return (
    <div className="flex justify-center -my-3 relative z-10">
      <Button
        onClick={onClick}
        variant="ghost"
        size="icon"
        className="text-white rounded-full border-4 border-gray-900 transition-all duration-200 bg-gray-800 hover:bg-gray-700 hover:scale-110"
      >
        <ArrowUpDown className="h-4 w-4" />
      </Button>
    </div>
  );
}
